import type {RawTimestamp} from '../firebase';
import type {ImageUrl, ParticipantBaseOld, UUIDString} from './shared';

export enum WorkoutType {
  WALK = 'WALK',
  RUN = 'RUN',
  HIIT = 'HIIT',
  WEIGHTS = 'WEIGHTS',
  CYCLING = 'CYCLING',
  SWIM = 'SWIM',
  YOGA = 'YOGA',
  SPORT = 'SPORT',
  CALISTHENICS = 'CALISTHENICS',
  PILATES = 'PILATES',
  MAX = 'MAX',
  /**
   * @deprecated use WEIGHTS or HIIT
   */
  STRENGTH = 'STRENGTH',
  /**
   * @deprecated use WALK, RUN, HIIT, CYCLING, SWIM
   */
  CARDIO = 'CARDIO',
  /**
   * @deprecated use YOGA, PILATES, CALISTHENICS
   */
  MOBILITY = 'MOBILITY',
}

// ORDERED list of how to display workout types
export const allWorkoutTypesToDisplay = [
  WorkoutType.WALK,
  WorkoutType.RUN,
  WorkoutType.HIIT,
  WorkoutType.WEIGHTS,
  WorkoutType.CYCLING,
  WorkoutType.SWIM,
  WorkoutType.YOGA,
  WorkoutType.SPORT,
  WorkoutType.CALISTHENICS,
  WorkoutType.PILATES,
  WorkoutType.MAX,
] as const satisfies readonly WorkoutType[];

type DisplayWorkoutTypes = (typeof allWorkoutTypesToDisplay)[number];

export const filterWorkoutTypes = (type: WorkoutType) =>
  allWorkoutTypesToDisplay.includes(type as DisplayWorkoutTypes);

export type WorkoutLink = {
  url: string;
  label: string;
  id: UUIDString;
};

export type Workout = {
  // user controlled fields
  workoutName: string;
  type: WorkoutType[];
  notes: string;
  trainerIds: UUIDString[];
  participantIds: UUIDString[];
  isCompleted?: true;
  links?: WorkoutLink[];
  images?: ImageUrl[];
  // metadata fields
  id: UUIDString;
  createdDateTime: RawTimestamp;
  startedDateTime: RawTimestamp;
  endedDateTime: RawTimestamp;
  lastUpdatedDateTime: RawTimestamp;
  /**
   * If present, indicates the parent workout of this workout,
   * implying this is a child workout given by a trainer.
   */
  parentWorkoutId?: UUIDString;
  copyWorkoutId?: UUIDString;
  childrenWorkoutIds?: string[];
};

export type WorkoutOld = Omit<Workout, 'trainerIds' | 'participantIds'> & {
  participants: ParticipantBaseOld[];
  trainers: ParticipantBaseOld[];
};

export const isOldWorkout = (workout: Partial<Workout>): workout is WorkoutOld =>
  'participants' in workout && 'trainers' in workout;

const FRIENDLY_WORKOUT_TYPES = {
  [WorkoutType.STRENGTH]: '🏋️ Strength',
  [WorkoutType.CARDIO]: '🏃 Cardio',
  [WorkoutType.MOBILITY]: '🤸‍♀️ Mobility',
  [WorkoutType.WEIGHTS]: '🏋️ Weights',
  [WorkoutType.RUN]: '🏃 Run',
  [WorkoutType.WALK]: '🚶 Walk',
  [WorkoutType.CYCLING]: '🚴 Cycling',
  [WorkoutType.SWIM]: '🏊 Swim',
  [WorkoutType.YOGA]: '🧘 Yoga',
  [WorkoutType.HIIT]: '⏱️ HIIT',
  [WorkoutType.SPORT]: '⚽️ Sport',
  [WorkoutType.CALISTHENICS]: '🤸‍♀️ Calisthenics',
  [WorkoutType.PILATES]: '🧘‍♀️ Pilates',
  [WorkoutType.MAX]: '🔝 Max',
} satisfies Record<WorkoutType, string>;

export const getWorkoutTypeLabel = (type: WorkoutType, hasNewLine?: boolean) => {
  const label = FRIENDLY_WORKOUT_TYPES[type];
  return hasNewLine ? label.replace(' ', '\n') : label;
};

const FRIENDLY_WORKOUT_TYPE_NAMES = {
  [WorkoutType.STRENGTH]: 'Strength',
  [WorkoutType.CARDIO]: 'Cardio',
  [WorkoutType.MOBILITY]: 'Mobility',
  [WorkoutType.WEIGHTS]: 'Weight training',
  [WorkoutType.RUN]: 'Running',
  [WorkoutType.WALK]: 'Walking',
  [WorkoutType.CYCLING]: 'Cycling',
  [WorkoutType.SWIM]: 'Swimming',
  [WorkoutType.YOGA]: 'Yoga',
  [WorkoutType.HIIT]: 'HIIT',
  [WorkoutType.SPORT]: 'Sport',
  [WorkoutType.CALISTHENICS]: 'Calisthenics',
  [WorkoutType.PILATES]: 'Pilates',
  [WorkoutType.MAX]: 'Max',
} satisfies Record<WorkoutType, string>;

export const getWorkoutNameFromType = (type: WorkoutType) => `${FRIENDLY_WORKOUT_TYPE_NAMES[type]} Workout`;

const WORKOUT_TYPE_ICONS = {
  [WorkoutType.STRENGTH]: 'weight-lifter',
  [WorkoutType.CARDIO]: 'run',
  [WorkoutType.MOBILITY]: 'yoga',
  [WorkoutType.WEIGHTS]: 'weight-lifter',
  [WorkoutType.RUN]: 'run',
  [WorkoutType.WALK]: 'walk',
  [WorkoutType.CYCLING]: 'bike',
  [WorkoutType.SWIM]: 'swim',
  [WorkoutType.YOGA]: 'yoga',
  [WorkoutType.CALISTHENICS]: 'weight-lifter',
  [WorkoutType.PILATES]: 'yoga',
  [WorkoutType.HIIT]: 'timer-outline',
  [WorkoutType.SPORT]: 'soccer',
  [WorkoutType.MAX]: 'arrow-up-bold-hexagon-outline',
} satisfies Record<WorkoutType, string>;

export const getWorkoutTypeIcon = (type: WorkoutType) => WORKOUT_TYPE_ICONS[type];
