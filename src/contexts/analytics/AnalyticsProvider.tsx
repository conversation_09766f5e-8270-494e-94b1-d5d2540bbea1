// eslint-disable-next-line @typescript-eslint/no-restricted-imports -- for analytics context
import {createContext, type ReactNode, useContext} from 'react';
import {isProd} from '@constants';
import type {AnalyticsService} from './analyticsService';
import {consoleAnalytics} from './consoleAnalytics';
import {firebaseAnalyticsService} from './firebaseAnalytics';

// Default to console analytics
const defaultService = consoleAnalytics();
// eslint-disable-next-line @typescript-eslint/naming-convention -- context naming convention
const AnalyticsContext = createContext<AnalyticsService>(defaultService);

export const useAnalytics = () => useContext(AnalyticsContext);

export const AnalyticsProvider: React.FC<{children: ReactNode}> = ({children}) => {
  // This can be changed to determine provider from Expo config extra.analyticsProvider
  const service: AnalyticsService = isProd ? firebaseAnalyticsService() : defaultService;

  return <AnalyticsContext.Provider value={service}>{children}</AnalyticsContext.Provider>;
};
