import {useRef, useState} from 'react';
import {Chip, Icon} from 'react-native-paper';
import {MultiSelect as MultiSelectInternal} from 'react-native-element-dropdown';
import type {IMultiSelectRef} from 'react-native-element-dropdown';
import {createStyleSheet, useAppTheme} from '@utils';
import {Box} from './Box';
import {Text} from './Text';
import {TextInputPaper} from './TextInput';

type SelectItem = {
  label: string;
  value: string;
};

type MultiSelectProps = Pick<React.ComponentProps<typeof MultiSelectInternal>, 'value' | 'onChange' | 'placeholder'> & {
  isDisabled?: boolean;
  isSearch?: boolean;
  options: SelectItem[];
};

const CloseIcon = () => (
  <Icon color='#fff' size={20} source='close' />
);

export const MultiSelect: React.FC<MultiSelectProps> = ({
  isDisabled = false,
  isSearch = false,
  onChange,
  options,
  placeholder,
  value,
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const theme = useAppTheme();
  const ref = useRef<IMultiSelectRef>(null);

  const isItemSelected = (item: SelectItem) =>
    value?.includes(item.value) ?? false;

  const renderItem = (item: SelectItem) => (
    <Box style={styles.itemContainer}>
      <Text style={[styles.itemText, theme.fonts.labelMedium]}>{item.label}</Text>
      {isItemSelected(item) && <Text mr={1} style={{lineHeight: 19}}>✅</Text>}
    </Box>
  );

  const renderRightIcon = () => (
    <Box style={styles.rightIconContainer}>
      <TextInputPaper.Icon
        color={theme.colors.dodgerBlueButtonBackground}
        icon={`arrow-${isFocused ? 'up' : 'down'}-drop-circle-outline`}
        onPress={() => ref.current?.open()}
      />
    </Box>
  );

  const renderSelectedItem = (item: SelectItem, unSelect?: (item: SelectItem) => void) => (
    <Chip
      closeIcon={CloseIcon}
      mode='outlined'
      showSelectedCheck={false}
      style={[styles.selectedChip, {backgroundColor: theme.colors.dodgerBlueButtonBackground}]}
      textStyle={styles.selectedChipText}
      onClose={() => unSelect?.(item)}
    >
      {item.label}
    </Chip>
  );

  return (
    <MultiSelectInternal
      ref={ref}
      data={options}
      disable={isDisabled}
      iconStyle={styles.iconStyle}
      labelField='label'
      {...(placeholder && {placeholder})}
      containerStyle={styles.containerStyle}
      fontFamily={theme.fonts.labelMedium.fontFamily}
      itemContainerStyle={styles.itemContainerStyle}
      placeholderStyle={[styles.placeholderStyle,
        {
          color: theme.colors.secondaryContainer2,
          fontSize: theme.fonts.labelMedium.fontSize,
        }]}
      renderItem={renderItem}
      renderRightIcon={renderRightIcon}
      renderSelectedItem={renderSelectedItem}
      search={isSearch}
      searchPlaceholder='Search...'
      selectedStyle={styles.selectedStyle}
      selectedTextStyle={[
        styles.selectedTextStyle,
        {color: theme.colors.primary},
        theme.fonts.labelMedium,
      ]}
      style={[
        styles.multiSelectStyle,
        {borderColor: theme.colors.cardBorderColor},
      ]}
      value={value}
      valueField='value'
      onBlur={() => setIsFocused(false)}
      onChange={onChange}
      onFocus={() => setIsFocused(true)}
    />
  );
};

const styles = createStyleSheet({
  itemContainer: {
    padding: 17,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  itemText: {
    flex: 1,
  },
  rightIconContainer: {
    position: 'absolute',
    right: 30,
    top: -2,
  },
  selectedChip: {
    marginRight: 6,
    marginTop: 8,
    paddingLeft: 0,
    borderRadius: 14,
    borderWidth: 0,
  },
  selectedChipText: {
    color: '#fff',
    textAlignVertical: 'center',
  },
  iconStyle: {},
  containerStyle: {
    borderRadius: 10,
  },
  itemContainerStyle: {
    borderRadius: 10,
  },
  placeholderStyle: {},
  selectedStyle: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 14,
    marginTop: 8,
    marginRight: 12,
    paddingHorizontal: 8,
    paddingVertical: 8,
  },
  selectedTextStyle: {},
  multiSelectStyle: {
    borderRadius: 10,
    paddingVertical: 12,
    paddingHorizontal: 10,
    borderWidth: 0,
    backgroundColor: '#fff',
  },
});
