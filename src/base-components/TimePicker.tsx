import {useRef} from 'react';
import {Picker} from '@react-native-picker/picker';
import ActionSheet, {type ActionSheetRef} from 'react-native-actions-sheet';
import {View} from 'react-native';
import {Box} from './Box';
import {Text} from './Text';
import {TextInput} from './TextInput';

type TimePickerProps = {
  isDisabled?: boolean | undefined;
  label: string;
  onChange: (value: {hours?: number; minutes?: number}) => void;
  value: {hours: number; minutes: number};
};

const HOURS = Array.from({length: 24}, (_, i) => i);
const MINUTES = Array.from({length: 60}, (_, i) => i);

// Utility function for formatting time display
const formatTimeDisplay = (hours: number, minutes: number): string =>
  `${hours}h, ${minutes}m`;

export const TimePicker: React.FC<TimePickerProps> = ({
  isDisabled,
  label,
  onChange,
  value,
}) => {
  const actionSheetRef = useRef<ActionSheetRef>(null);

  const handlePress = () => {
    if (!isDisabled) {
      actionSheetRef.current?.show();
    }
  };

  const displayValue = formatTimeDisplay(value.hours, value.minutes);

  return (
    <>
      <TextInput
        editable={false}
        label={label}
        mode='outlined'
        value={displayValue}
        onPress={handlePress}
      />

      <Box width='100%'>
        <ActionSheet
          ref={actionSheetRef}
          isModal
          gestureEnabled={false}
        >
          <Box flexDirection='row' justifyContent='space-between' pt={1}>
            <Box flex={1} />
            <Box style={{flex: 1}}>
              <Text pt={0.5} textAlign='center' variant='labelLarge'>{label}</Text>
            </Box>
            <Box style={{flex: 1, alignItems: 'flex-end'}}>
              {/* <Button mode='outlined' mr={1} onPress={() => actionSheetRef.current?.hide()}>Done</Button> */}
            </Box>
          </Box>
          <Box flexDirection='row' pb={4}>
            <View style={{flex: 1}}>
              <Picker
                itemStyle={{fontSize: 16}}
                selectedValue={value.hours}
                onValueChange={hours => onChange({hours})}
              >
                {HOURS.map(hour => (
                  <Picker.Item key={hour} label={`${hour}h`} value={hour} />
                ))}
              </Picker>
            </View>
            <View style={{flex: 1}}>
              <Picker
                itemStyle={{fontSize: 16}}
                selectedValue={value.minutes}
                onValueChange={minutes => onChange({minutes})}
              >
                {MINUTES.map(minute => (
                  <Picker.Item key={minute} label={`${minute}m`} value={minute} />
                ))}
              </Picker>
            </View>
          </Box>
        </ActionSheet>
      </Box>
    </>
  );
};
