import {LOGGER, onFirebaseRequest} from '../../../firebase';
import {iterateAppUsersExport} from '../../../firestore';
import {type AppUser, getIsoStringFromDate, StatusCodes, timestampToDate} from '../../../types';
import {composeMiddleware, with<PERSON><PERSON><PERSON><PERSON>, withValidGetRequest, writeToGoogleSheets} from '../../../utils';
import {DEFAULT_TIME_ZONE} from '../../../constants';

type AppUserExport = Pick<AppUser, 'email' | 'phoneNumber' | 'firstName' | 'lastName' | 'lastHealthStatsSync'>;

const convertToGoogleSheetFormat = (appUsers: AppUserExport[]): string[][] => {
  // Create headers
  const headers = ['Email', 'Phone Number', 'First Name', 'Last Name', 'Last Health Stats Sync'];

  // Generate rows
  const rows = appUsers.map(user => [
    user.email || '',
    user.phoneNumber || '',
    user.firstName || '',
    user.lastName || '',
    user.lastHealthStatsSync ? timestampToDate(user.lastHealthStatsSync).toISOString() : '',
  ]);

  // Combine headers and rows
  return [headers, ...rows];
};

const TARGET_SPREADSHEET_ID = '1eqRPf5JvMAkBilWI6iUaa0DXQ2IPLWdsj2gj6gZ4yoU';
const TARGET_SPREADSHEET_URI = `https://docs.google.com/spreadsheets/d/${TARGET_SPREADSHEET_ID}/edit`;

export const getAppUsersAsExport = onFirebaseRequest(
  composeMiddleware(
    'getAppUsersAsExport',
    async (_request, response) => {
      LOGGER.debug('Starting app user export...');

      const result: AppUserExport[] = [];
      await iterateAppUsersExport(async appUser => {
        result.push(appUser);
      });

      LOGGER.debug(`Finished app user export with ${result.length} users`);

      // Convert to Google Sheets format and write to sheet
      const formattedData = convertToGoogleSheetFormat(result);
      const todayIsoDate = getIsoStringFromDate(new Date(), DEFAULT_TIME_ZONE);
      await writeToGoogleSheets(formattedData, TARGET_SPREADSHEET_ID, `App Users Export on ${todayIsoDate}`);

      LOGGER.debug(`Successfully exported ${result.length} app users to Google Sheets`);

      response
        .status(StatusCodes.OK_200)
        .send({
          message: `Successfully exported ${result.length} app users to Google Sheets`,
          link: TARGET_SPREADSHEET_URI,
          data: result,
        })
        .end();
    },
    withValidGetRequest,
    withApiKey('************************************'),
  ),
);
