name: PR Tests

on:
  pull_request:
    branches:
      - main

jobs:
  test:
    name: Run Tests
    runs-on: macos-13 # ubuntu-latest-4-cores # must run with a runner with 8GB+ of RAM
    env:
      NODE_OPTIONS: "--max-old-space-size=8192"
    steps:
      - name: 🏗️ Checkout code
        uses: actions/checkout@v4

      - name: 🏗️ Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: npm
          cache-dependency-path: |
            package-lock.json
            functions/package-lock.json
            functions/report-generation/package-lock.json
            functions/swagger-docs/package-lock.json
            web-sign-up/package-lock.json

      - name: 📦 Install dependencies (all modules)
        run: npm run ci:all

      - name: 🧪 Run tests
        run: npm run test:ci
