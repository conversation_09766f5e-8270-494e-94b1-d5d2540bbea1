# .augmentignore - Additional files for Augment AI to ignore when indexing
# Note: .gitignore is still used, this is for additional exclusions

# Screenshot and mockup files (not needed for code understanding)
assets/screenshots/
*.mockup

# Build logs and temporary files
*.log
build.*.log
nohup.out

# Credentials and sensitive files (security)
credentials/
*.jks
*.keystore
*.p12
*.mobileprovision
gh_token

# Build artifacts (auto-generated)
functions/dist/
web-sign-up/dist-*/
*.tsbuildinfo

# Package manager lock files (auto-generated, very large)
package-lock.json
yarn.lock
Brewfile.lock.json

# Android build artifacts (auto-generated)
android/build/
android/gradle/wrapper/
android/gradlew*
android/app/build/
android/app/debug.keystore

# Temporary and cache files
.metro-health-check*
.eslintcache

# Generated report files
web-sign-up/stats.html
functions/report-generation/report.html
functions/report-generation/reports/

# Script output files (temporary data)
scripts/*.json
functions/firebase-functions-test-output.txt

# Workspace files
*.code-workspace
