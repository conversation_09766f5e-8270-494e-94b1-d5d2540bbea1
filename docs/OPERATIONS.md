# Operations

This document contains operational resources for managing the FlyFit project, including all important links, monitoring dashboards, and backup procedures.

> [!NOTE]
> **For new developers**: Complete the [Getting Started Guide](./GETTING_STARTED.md) and [Contributing Guidelines](./CONTRIBUTING.md) before using these operational resources.

## Project Links

Here are all of the links and bookmarks used to manage the FlyFit project.

### General Links

- [🗒️ Kanban/Trello Board](https://trello.com/b/Z6sPJTtg/flyfit-kanban-board)
- [🐙 FlyFit Github Repo](https://github.com/FlyBodies/fly-fit)
- [🖋️ Excalidraw/Planning Board](https://excalidraw.com/#room=930d8e1f8a3d6cad2c8a,Ux5DZ6bMyHdB_Jc9wVI9Ig)
- [❓ FlyFit FAQ](https://www.fly-bodies.com/flyfit-faqs)
- [🪽 FlyFit App Marketing landing page](https://www.fly-bodies.com/flyfit-app)

### Development Links
- [🏗️ Expo Project](https://expo.dev/accounts/fly-bodies/projects/fly-fit/builds)
- [🔥 Firebase fly-fit prod](https://console.firebase.google.com/u/0/project/fly-fit/firestore)
- [🔥 Firebase fly-fit-dev dev](https://console.firebase.google.com/u/0/project/fly-fit-dev/firestore)
- [☁️ Cloud Functions prod](https://console.cloud.google.com/run?project=fly-fit)
- [☁️ Cloud Functions dev](https://console.cloud.google.com/run?project=fly-fit-dev)
- [☁️ Error Logs prod](https://cloudlogging.app.goo.gl/j4kAVu2cAwvx9o6a9)
- [☁️ Error Logs dev](https://cloudlogging.app.goo.gl/Bav6cj3xGbVPLHhb6)
- [🔎 Algolia Search Index prod](https://dashboard.algolia.com/apps/2CB9SEQA0G/explorer/browse/algoliaBaseAppUsersIndex)
- [🔎 Algolia Search Index dev](https://dashboard.algolia.com/apps/OKPOYAN2A2/explorer/browse/algoliaBaseAppUsersIndex)
- [📧 Resend emails](https://resend.com/emails)
- [🔒 Dotenv Vault](https://vault.dotenv.org/ui/ui1/project/RoC69x/environment/4mCPM3p)
- [🏃 Fitbit app integration](https://dev.fitbit.com/login)

### Management Links

- [🍎 Apple App Store Connect](https://appstoreconnect.apple.com/apps/6450652563/appstore/ios/version/inflight)
- [🤖 Google Play Console](https://play.google.com/console/u/0/developers/5892814659496571933/app/4973741014673037397/releases/overview)
- [🍎 FlyFit Apple App Store Listing](https://apps.apple.com/us/app/fly-fit-fitness/id6450652563)
- [🤖 FlyFit Google Play Listing](https://play.google.com/store/apps/details?id=com.flybodies.flyfit&hl=en&gl=US)
- [🍎 FlyFit Apple App marketing tools](https://toolbox.marketingtools.apple.com/en-us/app-store/us/app/6450652563)

## Backing Up Data

Backups are scheduled to run every 7 days and are stored for 60 days. See the [GCP Disaster Recovery page](https://console.cloud.google.com/firestore/databases/-default-/disaster-recovery?project=fly-fit) for these backups.
