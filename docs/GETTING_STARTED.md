# Getting Started

This guide walks you through setting up the FlyFit development environment. **This is a complex setup process** that requires multiple tools, accounts, and certificates to setup all pieces of the project fully. Plan for 2-3 hours for your first setup.
## Overview

FlyFit development requires:
- **macOS** (required for iOS development)
- **Multiple service accounts** (GitHub, Firebase, Expo, Dotenv)
- **Native development tools** (Xcode, Android Studio)
- **Development certificates** (for device/simulator builds)

## Prerequisites

### Required Tools & Accounts

**Development Environment:**
- ✅ **macOS** - Required for iOS app builds
- ✅ **[Homebrew](https://brew.sh/)** - Package manager for macOS tools
  - Install: Run the ["Default Tap Cloning"](https://docs.brew.sh/Installation#default-tap-cloning) command
- ✅ **[Xcode](https://apps.apple.com/us/app/xcode)** - iOS development tools (~15GB download)
  - Select latest iOS version when prompted for platforms

**Service Accounts (Request Access):**
- ✅ **[GitHub](https://github.com/FlyBodies/fly-fit)** - Code repository access
  - Must be added to FlyBodies organization
- ✅ **[Firebase](https://console.firebase.google.com/u/0/project/fly-fit/overview)** - Backend services
  - Need access to both `fly-fit` and `fly-fit-dev` projects
- ✅ **[Dotenv Vault](https://vault.dotenv.org/ui/ui1/project/RoC69x/environment/4mCPM3p)** - Environment variables
  - Required for accessing sensitive configuration
- ✅ **[Expo/EAS](https://expo.dev/accounts/fly-bodies/projects/fly-fit)** - Build services *(optional for local dev)*
  - Only needed for creating production builds

> [!NOTE]
> **Need access?** Contact the team lead to be added to all required organizations and projects.

### Validation Checklist

Before proceeding, ensure you have:
- [ ] macOS machine with admin access
- [ ] Access to all service accounts listed above
- [ ] Stable internet connection (large downloads ahead)
- [ ] 2-3 hours available for setup

## Project Setup

> [!IMPORTANT]
> **Follow these steps in order.** Each step depends on the previous ones completing successfully.

### Step 1: Prepare Your Environment

1. **Open Terminal**
    ```bash
    # Open Terminal app on your Mac
    ```

2. **Set up Git workspace** *(skip if you already have a git directory)*
    ```bash
    cd ~                    # Go to home directory
    mkdir git              # Create git directory
    cd git                 # Enter git directory
    ```

### Step 2: Clone and Install

1. **Clone the repository**
    ```bash
    git clone https://github.com/FlyBodies/fly-fit
    cd fly-fit
    ```

2. **Install development tools**
    ```bash
    brew bundle
    ```
    > This installs: VSCode, Node Version Manager (NVM), Android Studio, and other required tools. If you don't want to do this, you are responsible for ensuring all of these tools are required.

3. **Set up Node.js**
    ```bash
    nvm install 22         # Install Node.js version 22
    nvm use 22             # Switch to Node.js 22
    node -v                # Verify: should show 22.x.x
    ```

4. **Open project in VSCode**
    ```bash
    code fly-fit           # Opens project in VSCode
    ```

### Step 3: Configure Project

1. **Install dependencies**
    ```bash
    npm install            # Install all project dependencies (~5-10 minutes)
    ```

2. **Run automated setup**
    ```bash
    npm run setup          # Follow all prompts carefully
    ```
    > This will prompt you to authenticate with Firebase, Dotenv, and Expo

3. **Verify setup** *(if any commands failed)*
    ```bash
    # Re-run individual setup commands if needed:
    npm run setup:firebase    # If Firebase auth failed
    npm run setup:eas         # If Expo auth failed
    npm run setup:dotenv      # If Dotenv auth failed
    ```

4. **Configure Xcode**
    ```bash
    sudo xcode-select -s /Applications/Xcode.app/Contents/Developer
    ```

### Validation

Verify your setup is complete:
- [ ] `node -v` shows version 22.x.x
- [ ] `firebase login` shows you're authenticated
- [ ] `eas whoami` shows your Expo account *(if configured)*
- [ ] VSCode opened the project successfully

## Running the Project

> [!WARNING]
> **Native builds required**: FlyFit uses native dependencies and requires development certificates. We do NOT use Expo Go.

### Understanding the Build Process

FlyFit requires **two separate processes**:
1. **Native build** - Compiles native code and installs on simulator/device
2. **Development server** - Serves JavaScript code with hot reloading

### First Time: Create Development Builds

**For iOS (requires Xcode and iOS Simulator):**
```bash
npm run ios:dev        # Builds native app + starts dev server (~10-15 minutes)
```

**For Android (requires Android Studio and emulator):**
```bash
npm run android:dev    # Builds native app + starts dev server (~10-15 minutes)
```

> [!NOTE]
> **First builds take time**: Native compilation is slow. Subsequent builds are faster.

### Development Server Controls

Once your build completes, you'll see these terminal controls:

```
› Press a │ open Android
› Press i │ open iOS simulator
› Press w │ open web
› Press r │ reload app
› Press m │ toggle menu
› Press ? │ show all commands
```

### Daily Development Workflow

**After your first build**, use this faster workflow:

```bash
npm start  # Start dev server only (uses existing native build)
```

> This serves JavaScript changes with hot reloading while keeping the same native dependencies.

### When to Rebuild

You need a **new native build** when:
- ✅ Adding new native dependencies
- ✅ Changing native configuration
- ✅ Switching between dev/prod environments
- ✅ After pulling major changes from `main`

You can use **dev server only** when:
- ✅ Changing JavaScript/TypeScript code
- ✅ Modifying React components
- ✅ Updating styles or assets

## Running the Backend Locally

> [!NOTE]
> **Optional for most development**: You can develop frontend features using the remote dev environment. Local backend is mainly for backend development.

### When You Need Local Backend

- ✅ Developing Firebase Functions
- ✅ Testing backend logic changes
- ✅ Working offline or with limited connectivity
- ✅ Debugging backend issues

### Setup Local Backend

1. **Generate service account key**
    ```bash
    # Follow the detailed steps in our Deployment Guide
    ```
    > See [Deployment Guide - Prerequisites](./DEPLOYMENT.md#prerequisites) for creating Google Cloud service account keys.

2. **Start Firebase emulators**
    ```bash
    npm run serve          # Starts local Firebase emulators
    ```

3. **Verify emulators are running**
    - Look for: `"Starting Firebase Emulator with the exported data..."` in terminal
    - Open emulator UI: http://localhost:4000

### Emulator Services

The local setup includes:
- **Firestore** - Database emulation
- **Functions** - Cloud Functions emulation
- **Auth** - Authentication emulation
- **Storage** - File storage emulation

## Next Steps

### ✅ Setup Complete!

You should now have:
- [ ] FlyFit running on iOS/Android simulator
- [ ] Development server with hot reloading
- [ ] VSCode with the project open
- [ ] All authentication configured

### 🚀 Ready to Develop

**Next steps in order:**
1. [**Contributing Guide**](./CONTRIBUTING.md) - Learn our development workflow and pick up your first task
2. [**Architecture Guide**](./ARCHITECTURE.md) - Understand the codebase structure and patterns
3. [**Deployment Guide**](./DEPLOYMENT.md) - Learn how to deploy your changes
4. [**Operations Guide**](./OPERATIONS.md) - Access project links and monitoring resources

## Troubleshooting

### Common Setup Issues

**Node.js version errors:**
```bash
nvm use 22             # Ensure you're using Node 22
node -v                # Verify version
```

**Xcode build errors:**
```bash
# Ensure Xcode command line tools are set correctly
sudo xcode-select -s /Applications/Xcode.app/Contents/Developer
```

**Android emulator not starting:**
- Open Android Studio
- Go to AVD Manager
- Create/start an Android Virtual Device

**App running with wrong environment:**
```bash
npm run clean          # Clean project
npm install            # Reinstall
npm run ios:dev        # Rebuild for correct environment
```

### Still Having Issues?

1. **Check error messages carefully** - they usually contain the solution
1. **Search the error online or with AI** - AI or Stack Overflow often have answers
1. **Escalate and ask the team** - We're here to help!
1. **Document the solution** - Help future developers by updating this guide
